version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
      - MONGODB_URL=${MONGODB_URL:-mongodb://localhost:27017}
      - MONGODB_DB_NAME=upvote_db
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - CORS_ORIGINS=${CORS_ORIGINS:-["*"]}
      - BTCPAY_SERVER_URL=${BTCPAY_SERVER_URL}
      - BTCPAY_API_KEY=${BTCPAY_API_KEY}
      - BTCPAY_STORE_ID=${BTCPAY_STORE_ID}
      - BTCPAY_WEBHOOK_SECRET=${BTCPAY_WEBHOOK_SECRET}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:5173}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
